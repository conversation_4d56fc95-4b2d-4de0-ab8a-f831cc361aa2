import uuid
import json
from datetime import datetime
import structlog
import grpc

from app.db.neo4j import execute_write_query, execute_read_query
from app.modules.organisation.repository.source import SourceQueries
from app.grpc_ import organisation_pb2
from app.utils.constants.sources import SourceType
from app.utils.constants.departments import DefaultDepartments
from app.utils.google_service_account import GoogleServiceAccountManager

logger = structlog.get_logger()

class SourceService:
    """Service handling source operations."""
    
    def __init__(self):
        self.queries = SourceQueries()
        self.google_sa_manager = GoogleServiceAccountManager()

    def addGoogleDriveSource(self, request, context):
        """
        Add a new Google Drive source with credentials to an organization.
        Supports service account keys.

        Args:
            request: Contains organisation_id, name, key
            context: gRPC context

        Returns:
            Response indicating success/failure and the created source
        """
        logger.info("Received request to add Google Drive source",
                   org_id=request.organisation_id)

        try:
            # Validate that key is provided
            if not request.key:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Service account key must be provided")
                return organisation_pb2.AddSourceResponse(
                    success=False,
                    message="Service account key must be provided"
                )

            key = request.key
            
            # Validate JSON format for service account key
            try:
                json.loads(key)
            except json.JSONDecodeError:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Invalid JSON format in service account key")
                return organisation_pb2.AddSourceResponse(
                    success=False,
                    message="Invalid JSON format in service account key"
                )

            # Set source type to Google Drive
            db_source_type = SourceType.GOOGLE_DRIVE.value

            # Check if a source of this type already exists for the organization
            existing_query = self.queries.CHECK_EXISTING_SOURCE
            existing_params = {
                "org_id": request.organisation_id,
                "source_type": db_source_type
            }

            existing_result = execute_read_query(existing_query, existing_params)

            if existing_result:
                logger.error(f"Google Drive source already exists for organization {request.organisation_id}")
                context.set_code(grpc.StatusCode.ALREADY_EXISTS)
                context.set_details(f"Google Drive source already exists for this organization")
                return organisation_pb2.AddSourceResponse(success=False, message=f"Google Drive source already exists for this organization")

            # Validate service account access
            validation_success, validation_message, accessible_folders = \
                self.google_sa_manager.validate_service_account_access(key)
            
            if not validation_success:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details(f"Service account validation failed: {validation_message}")
                return organisation_pb2.AddSourceResponse(
                    success=False,
                    message=f"Service account validation failed: {validation_message}"
                )
            
            # Generate a unique ID for the source
            source_id = str(uuid.uuid4())
            current_time = datetime.utcnow().isoformat()

            # Create the source node in Neo4j using repository
            query = self.queries.CREATE_SOURCE
            params = {
                "org_id": request.organisation_id,
                "id": source_id,
                "name": request.name,
                "type": db_source_type,
                "key": key,
                "jira_url": None,
                "jira_email": None,
                "is_validated": validation_success,
                "validation_message": validation_message,
                "last_validated_at": current_time if validation_success else None,
                "created_at": current_time,
                "updated_at": current_time
            }

            result = execute_write_query(query, params)

            if not result:
                logger.error("Failed to create Google Drive source in Neo4j")
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details("Failed to create Google Drive source")
                return organisation_pb2.AddSourceResponse(success=False, message="Failed to create Google Drive source")

            # If service account validation was successful and we have folders, create folder nodes
            if validation_success and accessible_folders:
                from app.modules.connectors.google_drive.services.google_drive_service import GoogleDriveService
                gdrive_service = GoogleDriveService()
                
                # Extract file_ids from request if provided
                file_ids = list(request.file_ids) if request.file_ids else None
                
                folder_creation_success, synced_files = gdrive_service.create_folder_nodes_with_permissions(
                    request.organisation_id, accessible_folders, file_ids
                )
                
                if folder_creation_success:
                    if file_ids:
                        validation_message += f" Created {len(accessible_folders)} folder nodes and synced {len(synced_files)} specific files with permission-based access."
                    else:
                        validation_message += f" Created {len(accessible_folders)} folder nodes with permission-based access."

            # Schedule automatic sync for Google Drive source
            try:
                # Schedule an immediate sync job for the newly added Google Drive source
                from app.modules.connectors.google_drive.services.google_drive_service import GoogleDriveService
                gdrive_service = GoogleDriveService()
                
                job_id = gdrive_service._schedule_sync(
                    user_id=request.organisation_id,  # Using organisation_id as user_id for scheduling
                    organisation_id=request.organisation_id,
                    full_sync=True,  # Full sync for new sources
                    delay_seconds=5  # Small delay to ensure source is fully created
                )
                
                logger.info(f"Automatic sync job scheduled for new Google Drive source",
                          organisation_id=request.organisation_id,
                          job_id=job_id)
                validation_message += f" Automatic sync job scheduled (ID: {job_id})."
                
            except Exception as sync_error:
                logger.warning(f"Failed to schedule automatic sync for new source: {str(sync_error)}")
                validation_message += " Note: Automatic sync scheduling failed, manual sync may be required."

            # Create source model for response
            source_model = organisation_pb2.SourceModel(
                id=source_id,
                organisation_id=request.organisation_id,
                type=organisation_pb2.SourceType.GOOGLE_DRIVE,
                name=request.name,
                created_at=current_time,
                updated_at=current_time
            )
            
            # Create file info objects for synced files
            file_info_objects = []
            if key and validation_success and 'synced_files' in locals():
                for file_data in synced_files:
                    file_info = organisation_pb2.FileInfo(
                        id=file_data['id'],
                        name=file_data['name']
                    )
                    file_info_objects.append(file_info)

            return organisation_pb2.AddSourceResponse(
                success=True,
                message=validation_message,
                source=source_model,
                synced_files=file_info_objects
            )

        except Exception as e:
            logger.error("Error adding Google Drive source", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error adding Google Drive source: {str(e)}")
            return organisation_pb2.AddSourceResponse(success=False, message=f"Error adding Google Drive source: {str(e)}")
    
    def addJiraSource(self, request, context):
        """
        Add a new Jira source with credentials to an organization.
        Supports API keys.

        Args:
            request: Contains organisation_id, name, key (used as API key),
                    jira_url, jira_email
            context: gRPC context

        Returns:
            Response indicating success/failure and the created source
        """
        logger.info("Received request to add Jira source",
                   org_id=request.organisation_id)

        try:
            # Validate that key is provided
            if not request.key:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("API key must be provided")
                return organisation_pb2.AddSourceResponse(
                    success=False,
                    message="API key must be provided"
                )

            key = request.key
            
            # Initialize Jira-specific variables
            jira_url = getattr(request, 'jira_url', None)
            jira_email = getattr(request, 'jira_email', None)
            
            # Set source type to Jira
            db_source_type = SourceType.JIRA.value

            existing_result = execute_read_query(self.queries.CHECK_EXISTING_SOURCE, {
                "org_id": request.organisation_id,
                "source_type": db_source_type
            })

            if existing_result:
                logger.error(f"Jira source already exists for organization {request.organisation_id}")
                context.set_code(grpc.StatusCode.ALREADY_EXISTS)
                context.set_details(f"Jira source already exists for this organization")
                return organisation_pb2.AddSourceResponse(success=False, message=f"Jira source already exists for this organization")

            # Check if Jira URL and email are provided
            if not jira_url:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Jira URL must be provided")
                return organisation_pb2.AddSourceResponse(
                    success=False,
                    message="Jira URL must be provided"
                )
            
            if not jira_email:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Jira email must be provided")
                return organisation_pb2.AddSourceResponse(
                    success=False,
                    message="Jira email must be provided"
                )
            
            # Basic validation - check that key is not empty
            if not key.strip():
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Invalid Jira API key")
                return organisation_pb2.AddSourceResponse(
                    success=False,
                    message="Invalid Jira API key"
                )
            
            # Validate Jira API token by making a request to the Jira API
            import requests
            from requests.auth import HTTPBasicAuth
            
            # Normalize Jira URL (remove trailing slash if present)
            if jira_url.endswith('/'):
                jira_url = jira_url[:-1]
            
            # Construct the API endpoint URL
            api_url = f"{jira_url}/rest/api/3/myself"
            
            try:
                # Make request to Jira API
                response = requests.get(
                    api_url,
                    auth=HTTPBasicAuth(jira_email, key),
                    headers={"Accept": "application/json"}
                )
                
                # Check if request was successful
                if response.status_code == 200:
                    validation_success = True
                    user_data = response.json()
                    validation_message = f"Jira API key validated successfully for user {user_data.get('displayName', jira_email)}"
                else:
                    validation_success = False
                    validation_message = f"Jira API key validation failed: {response.status_code} - {response.text}"
                    context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                    context.set_details(validation_message)
                    return organisation_pb2.AddSourceResponse(
                        success=False,
                        message=validation_message
                    )
            except Exception as e:
                validation_success = False
                validation_message = f"Error validating Jira API key: {str(e)}"
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(validation_message)
                return organisation_pb2.AddSourceResponse(
                    success=False,
                    message=validation_message
                )

            # Generate a unique ID for the source
            source_id = str(uuid.uuid4())
            current_time = datetime.utcnow().isoformat()

            # Create the source node in Neo4j using repository
            params = {
                "org_id": request.organisation_id,
                "id": source_id,
                "name": request.name,
                "type": db_source_type,
                "key": key,
                "jira_url": jira_url,
                "jira_email": jira_email,
                "is_validated": validation_success,
                "validation_message": validation_message,
                "last_validated_at": current_time if validation_success else None,
                "created_at": current_time,
                "updated_at": current_time
            }

            result = execute_write_query(self.queries.CREATE_SOURCE, params)

            if not result:
                logger.error("Failed to create Jira source in Neo4j")
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details("Failed to create Jira source")
                return organisation_pb2.AddSourceResponse(success=False, message="Failed to create Jira source")

            # Create source model for response
            source_model = organisation_pb2.SourceModel(
                id=source_id,
                organisation_id=request.organisation_id,
                type=organisation_pb2.SourceType.JIRA,
                name=request.name,
                created_at=current_time,
                updated_at=current_time
            )

            return organisation_pb2.AddSourceResponse(
                success=True,
                message=validation_message,
                source=source_model,
                synced_files=[]
            )

        except Exception as e:
            logger.error("Error adding Jira source", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error adding Jira source: {str(e)}")
            return organisation_pb2.AddSourceResponse(success=False, message=f"Error adding Jira source: {str(e)}")
            
    def addSource(self, request, context):
        """
        Add a new source with credentials to an organization.
        This is a legacy method that routes to the appropriate source-specific method.

        Args:
            request: Contains organisation_id, type, name, key (used as generic key),
                    jira_url, jira_email (for Jira sources)
            context: gRPC context

        Returns:
            Response indicating success/failure and the created source
        """
        logger.info("Received request to add source (legacy method)",
                   org_id=request.organisation_id,
                   source_type=request.type)

        # Route to the appropriate source-specific method based on type
        if request.type == organisation_pb2.SourceType.GOOGLE_DRIVE:
            return self.addGoogleDriveSource(request, context)
        elif request.type == organisation_pb2.SourceType.JIRA:
            return self.addJiraSource(request, context)
        else:
            logger.error(f"Unsupported source type: {request.type}")
            context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
            context.set_details(f"Unsupported source type: {request.type}")
            return organisation_pb2.AddSourceResponse(success=False, message=f"Unsupported source type: {request.type}")

    def listSources(self, request, context):
        """
        List all sources for an organization.

        Args:
            request: Contains organisation_id
            context: gRPC context

        Returns:
            Response containing a list of sources and isInitialMapping flag
        """
        logger.info("Received request to list sources", org_id=request.organisation_id)

        try:
            query = self.queries.LIST_SOURCES
            params = {
                "org_id": request.organisation_id
            }

            result = execute_read_query(query, params)

            sources = []
            for record in result:
                source = record.get('s', {})
                
                # Map database source type to protobuf enum
                db_source_type = source.get('type', SourceType.GOOGLE_DRIVE.value)
                
                db_to_proto_mapping = {
                    SourceType.GOOGLE_DRIVE.value: organisation_pb2.SourceType.GOOGLE_DRIVE,
                    SourceType.SLACK.value: organisation_pb2.SourceType.SLACK
                }
                
                proto_source_type = db_to_proto_mapping.get(db_source_type, organisation_pb2.SourceType.GOOGLE_DRIVE)

                source_model = organisation_pb2.SourceModel(
                    id=source.get('id'),
                    organisation_id=request.organisation_id,
                    type=proto_source_type,
                    name=source.get('name'),
                    created_at=source.get('created_at'),
                    updated_at=source.get('updated_at')
                )
                sources.append(source_model)
            
            # Check if any department other than general has access to folders
            is_initial_mapping_query = """
            MATCH (o:Organisation {id: $org_id})-[:HAS_DEPARTMENT]->(d:Department)
            WHERE NOT toLower(d.name) = toLower($general_dept)
            MATCH (d)-[:HAS_ACCESS]->(f:GoogleDriveFolder)
            RETURN COUNT(f) > 0 as hasMapping
            """
            
            is_initial_mapping_params = {
                "org_id": request.organisation_id,
                "general_dept": DefaultDepartments.GENERAL.value
            }
            
            mapping_result = execute_read_query(is_initial_mapping_query, is_initial_mapping_params)
            is_initial_mapping = mapping_result[0]['hasMapping'] if mapping_result else False

            return organisation_pb2.ListSourcesResponse(
                success=True,
                message=f"Found {len(sources)} sources",
                sources=sources,
                isInitialMapping=is_initial_mapping
            )

        except Exception as e:
            logger.error("Error listing sources", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error listing sources: {str(e)}")
            return organisation_pb2.ListSourcesResponse(success=False, message=f"Error listing sources: {str(e)}")

    def deleteSource(self, request, context):
        """
        Delete a source.

        Args:
            request: Contains source_id and user_id
            context: gRPC context

        Returns:
            Response indicating success/failure
        """
        logger.info("Received request to delete source", source_id=request.source_id, user_id=request.user_id)

        try:
            # Validate user is an admin of the organization that owns the source
            admin_query = self.queries.VALIDATE_SOURCE_DELETE_PERMISSION
            admin_params = {
                "user_id": request.user_id,
                "source_id": request.source_id
            }

            admin_result = True #execute_read_query(admin_query, admin_params)

            if not admin_result:
                logger.error(f"User {request.user_id} is not authorized to delete source {request.source_id}")
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details(f"Only organization admins can delete sources")
                return organisation_pb2.DeleteSourceResponse(success=False, message=f"Permission denied")

            # Delete the source using repository
            query = self.queries.DELETE_SOURCE
            params = {
                "source_id": request.source_id
            }

            execute_write_query(query, params)

            return organisation_pb2.DeleteSourceResponse(
                success=True,
                message="Source deleted successfully"
            )

        except Exception as e:
            logger.error("Error deleting source", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error deleting source: {str(e)}")
            return organisation_pb2.DeleteSourceResponse(success=False, message=f"Error deleting source: {str(e)}")

    def updateGoogleDriveSourceCredentials(self, request, context, source_data=None, organisation_id=None):
        """
        Update credentials for an existing Google Drive source.
        """
        logger.info("Received request to update Google Drive source credentials",
                   source_id=request.source_id, user_id=request.user_id)

        try:
            # Validate that key is provided
            if not request.key:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Service account key must be provided")
                return organisation_pb2.UpdateSourceCredentialsResponse(
                    success=False,
                    message="Service account key must be provided"
                )

            # If source_data and organisation_id are not provided, get them
            if not source_data or not organisation_id:
                # Get existing source
                source_query = self.queries.GET_SOURCE_BY_ID
                source_params = {"source_id": request.source_id}
                source_result = execute_read_query(source_query, source_params)

                if not source_result:
                    context.set_code(grpc.StatusCode.NOT_FOUND)
                    context.set_details("Source not found")
                    return organisation_pb2.UpdateSourceCredentialsResponse(
                        success=False,
                        message="Source not found"
                    )

                source_data = source_result[0]['s']
                organisation_id = source_result[0]['org_id']

            # Store the key
            key = request.key
            
            # Validate JSON format for Google Drive key
            try:
                json.loads(key)
            except json.JSONDecodeError:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Invalid JSON format in key")
                return organisation_pb2.UpdateSourceCredentialsResponse(
                    success=False,
                    message="Invalid JSON format in key"
                )

            # Validate service account access
            validation_success, validation_message, accessible_folders = \
                self.google_sa_manager.validate_service_account_access(key)
            
            if not validation_success:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details(f"Service account validation failed: {validation_message}")
                return organisation_pb2.UpdateSourceCredentialsResponse(
                    success=False,
                    message=f"Service account validation failed: {validation_message}"
                )

            # Update source credentials
            current_time = datetime.utcnow().isoformat()
            
            # Use the standard update query for Google Drive
            update_query = self.queries.UPDATE_SOURCE_CREDENTIALS
            update_params = {
                "source_id": request.source_id,
                "key": key,
                "updated_at": current_time
            }

            execute_write_query(update_query, update_params)

            # Update validation status
            validation_query = self.queries.UPDATE_SOURCE_VALIDATION_STATUS
            validation_params = {
                "source_id": request.source_id,
                "is_validated": validation_success,
                "last_validated_at": current_time if validation_success else None,
                "validation_message": validation_message
            }

            execute_write_query(validation_query, validation_params)

            # Create updated source model for response
            source_model = organisation_pb2.SourceModel(
                id=request.source_id,
                organisation_id=organisation_id,
                type=organisation_pb2.SourceType.GOOGLE_DRIVE,
                name=source_data.get('name', ''),
                created_at=source_data.get('created_at', ''),
                updated_at=current_time
            )

            return organisation_pb2.UpdateSourceCredentialsResponse(
                success=True,
                message=validation_message,
                source=source_model
            )

        except Exception as e:
            logger.error("Error updating Google Drive source credentials", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error updating Google Drive source credentials: {str(e)}")
            return organisation_pb2.UpdateSourceCredentialsResponse(
                success=False,
                message=f"Error updating Google Drive source credentials: {str(e)}"
            )
    
    def updateJiraSourceCredentials(self, request, context, source_data=None, organisation_id=None):
        """
        Update credentials for an existing Jira source.
        """
        logger.info("Received request to update Jira source credentials",
                   source_id=request.source_id, user_id=request.user_id)

        try:
            # Validate that key is provided
            if not request.key:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("API key must be provided")
                return organisation_pb2.UpdateSourceCredentialsResponse(
                    success=False,
                    message="API key must be provided"
                )

            # If source_data and organisation_id are not provided, get them
            if not source_data or not organisation_id:
                # Get existing source
                source_query = self.queries.GET_SOURCE_BY_ID
                source_params = {"source_id": request.source_id}
                source_result = execute_read_query(source_query, source_params)

                if not source_result:
                    context.set_code(grpc.StatusCode.NOT_FOUND)
                    context.set_details("Source not found")
                    return organisation_pb2.UpdateSourceCredentialsResponse(
                        success=False,
                        message="Source not found"
                    )

                source_data = source_result[0]['s']
                organisation_id = source_result[0]['org_id']

            # Store the key
            key = request.key
            
            # Get Jira-specific fields if available
            jira_url = getattr(request, 'jira_url', None)
            jira_email = getattr(request, 'jira_email', None)
            
            # For Jira, we need URL and email
            if not jira_url and not source_data.get('jira_url'):
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Jira URL must be provided")
                return organisation_pb2.UpdateSourceCredentialsResponse(
                    success=False,
                    message="Jira URL must be provided"
                )
            
            if not jira_email and not source_data.get('jira_email'):
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Jira email must be provided")
                return organisation_pb2.UpdateSourceCredentialsResponse(
                    success=False,
                    message="Jira email must be provided"
                )
            
            # Use provided values or existing values from the database
            current_jira_url = jira_url if jira_url else source_data.get('jira_url')
            current_jira_email = jira_email if jira_email else source_data.get('jira_email')
            
            # Basic validation for Jira API key
            if not key.strip():
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Invalid Jira API key")
                return organisation_pb2.UpdateSourceCredentialsResponse(
                    success=False,
                    message="Invalid Jira API key"
                )
            
            # Validate Jira API token by making a request to the Jira API
            import requests
            from requests.auth import HTTPBasicAuth
            
            # Normalize Jira URL (remove trailing slash if present)
            if current_jira_url.endswith('/'):
                current_jira_url = current_jira_url[:-1]
            
            # Construct the API endpoint URL
            api_url = f"{current_jira_url}/rest/api/3/myself"
            
            try:
                # Make request to Jira API
                response = requests.get(
                    api_url,
                    auth=HTTPBasicAuth(current_jira_email, key),
                    headers={"Accept": "application/json"}
                )
                
                # Check if request was successful
                if response.status_code == 200:
                    validation_success = True
                    user_data = response.json()
                    validation_message = f"Jira API key validated successfully for user {user_data.get('displayName', current_jira_email)}"
                else:
                    validation_success = False
                    validation_message = f"Jira API key validation failed: {response.status_code} - {response.text}"
                    context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                    context.set_details(validation_message)
                    return organisation_pb2.UpdateSourceCredentialsResponse(
                        success=False,
                        message=validation_message
                    )
            except Exception as e:
                validation_success = False
                validation_message = f"Error validating Jira API key: {str(e)}"
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(validation_message)
                return organisation_pb2.UpdateSourceCredentialsResponse(
                    success=False,
                    message=validation_message
                )

            # Update source credentials
            current_time = datetime.utcnow().isoformat()
            
            # For Jira, update URL and email if provided
            update_query = """
            MATCH (s:Source {id: $source_id})
            SET s.key = $key,
                s.jira_url = $jira_url,
                s.jira_email = $jira_email,
                s.updated_at = $updated_at
            RETURN s
            """
            
            update_params = {
                "source_id": request.source_id,
                "key": key,
                "jira_url": jira_url if jira_url else source_data.get('jira_url'),
                "jira_email": jira_email if jira_email else source_data.get('jira_email'),
                "updated_at": current_time
            }

            execute_write_query(update_query, update_params)

            # Update validation status
            validation_query = self.queries.UPDATE_SOURCE_VALIDATION_STATUS
            validation_params = {
                "source_id": request.source_id,
                "is_validated": validation_success,
                "last_validated_at": current_time if validation_success else None,
                "validation_message": validation_message
            }

            execute_write_query(validation_query, validation_params)

            # Create updated source model for response
            source_model = organisation_pb2.SourceModel(
                id=request.source_id,
                organisation_id=organisation_id,
                type=organisation_pb2.SourceType.JIRA,
                name=source_data.get('name', ''),
                created_at=source_data.get('created_at', ''),
                updated_at=current_time
            )

            return organisation_pb2.UpdateSourceCredentialsResponse(
                success=True,
                message=validation_message,
                source=source_model
            )

        except Exception as e:
            logger.error("Error updating Jira source credentials", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error updating Jira source credentials: {str(e)}")
            return organisation_pb2.UpdateSourceCredentialsResponse(
                success=False,
                message=f"Error updating Jira source credentials: {str(e)}"
            )
    
    def updateSourceCredentials(self, request, context):
        """
        Update credentials for an existing source.
        This is a legacy method that routes to the appropriate source-specific method.
        """
        logger.info("Received request to update source credentials (legacy method)",
                   source_id=request.source_id, user_id=request.user_id)

        try:
            # Get existing source to determine its type
            source_result = execute_read_query(self.queries.GET_SOURCE_BY_ID, {"source_id": request.source_id})

            if not source_result:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Source not found")
                return organisation_pb2.UpdateSourceCredentialsResponse(
                    success=False,
                    message="Source not found"
                )

            source_data = source_result[0]['s']
            organisation_id = source_result[0]['org_id']

            # Route to the appropriate source-specific method based on type
            if source_data.get('type') == SourceType.GOOGLE_DRIVE.value:
                return self.updateGoogleDriveSourceCredentials(request, context, source_data, organisation_id)
            elif source_data.get('type') == SourceType.JIRA.value:
                return self.updateJiraSourceCredentials(request, context, source_data, organisation_id)
            else:
                logger.error(f"Unsupported source type: {source_data.get('type')}")
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details(f"Unsupported source type: {source_data.get('type')}")
                return organisation_pb2.UpdateSourceCredentialsResponse(
                    success=False,
                    message=f"Unsupported source type: {source_data.get('type')}"
                )

        except Exception as e:
            logger.error("Error updating source credentials", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error updating source credentials: {str(e)}")
            return organisation_pb2.UpdateSourceCredentialsResponse(
                success=False,
                message=f"Error updating source credentials: {str(e)}"
            )
    
    def validateSource(self, request, context):
        """
        Validate a source and return accessible folders.
        """
        logger.info("Received request to validate source", 
                   source_id=request.source_id, org_id=request.organisation_id)

        try:
            # Get source data
            source_query = self.queries.GET_SOURCE_BY_ID
            source_params = {"source_id": request.source_id}
            source_result = execute_read_query(source_query, source_params)

            if not source_result:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Source not found")
                return organisation_pb2.ValidateSourceResponse(
                    success=False,
                    message="Source not found",
                    accessible_folders=[]
                )

            source_data = source_result[0]['s']
            key = source_data.get('key')  # Using key field as a generic key
            jira_url = source_data.get('jira_url')
            jira_email = source_data.get('jira_email')

            # Validate credentials based on source type
            if source_data.get('type') == SourceType.GOOGLE_DRIVE.value:
                validation_success, validation_message, accessible_folders = \
                    self.google_sa_manager.validate_service_account_access(key)
            elif source_data.get('type') == SourceType.JIRA.value:
                # Validate Jira API token by making a request to the Jira API
                import requests
                from requests.auth import HTTPBasicAuth
                
                # Check if we have all required Jira information
                if not jira_url or not jira_email or not key:
                    validation_success = False
                    validation_message = "Missing Jira configuration (URL, email, or API key)"
                    accessible_folders = []
                else:
                    # Normalize Jira URL (remove trailing slash if present)
                    if jira_url.endswith('/'):
                        jira_url = jira_url[:-1]
                    
                    # Construct the API endpoint URL
                    api_url = f"{jira_url}/rest/api/3/myself"
                    
                    try:
                        # Make request to Jira API
                        response = requests.get(
                            api_url,
                            auth=HTTPBasicAuth(jira_email, key),
                            headers={"Accept": "application/json"}
                        )
                        
                        # Check if request was successful
                        if response.status_code == 200:
                            validation_success = True
                            user_data = response.json()
                            validation_message = f"Jira API key validated successfully for user {user_data.get('displayName', jira_email)}"
                        else:
                            validation_success = False
                            validation_message = f"Jira API key validation failed: {response.status_code} - {response.text}"
                        
                        accessible_folders = []  # Jira doesn't have folders like Google Drive
                    except Exception as e:
                        validation_success = False
                        validation_message = f"Error validating Jira API key: {str(e)}"
                        accessible_folders = []
                
                # Convert folders to proto format
                proto_folders = []
                for folder in accessible_folders:
                    proto_folder = organisation_pb2.Folder(
                        id=folder['id'],
                        name=folder['name']
                    )
                    proto_folders.append(proto_folder)
                
                return organisation_pb2.ValidateSourceResponse(
                    success=validation_success,
                    message=validation_message,
                    accessible_folders=proto_folders
                )
            
            else:
                return organisation_pb2.ValidateSourceResponse(
                    success=False,
                    message="No valid service account credentials found",
                    accessible_folders=[]
                )

        except Exception as e:
            logger.error("Error validating source", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error validating source: {str(e)}")
            return organisation_pb2.ValidateSourceResponse(
                success=False,
                message=f"Error validating source: {str(e)}",
                accessible_folders=[]
            )

    def deleteSource(self, request, context):
        """
        Delete a source.

        Args:
            request: Contains source_id and user_id
            context: gRPC context

        Returns:
            Response indicating success/failure
        """
        logger.info("Received request to delete source", source_id=request.source_id, user_id=request.user_id)

        try:
            # Validate user is an admin of the organization that owns the source
            admin_query = self.queries.VALIDATE_SOURCE_DELETE_PERMISSION
            admin_params = {
                "user_id": request.user_id,
                "source_id": request.source_id
            }

            admin_result = True #execute_read_query(admin_query, admin_params)

            if not admin_result:
                logger.error(f"User {request.user_id} is not authorized to delete source {request.source_id}")
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details(f"Only organization admins can delete sources")
                return organisation_pb2.DeleteSourceResponse(success=False, message=f"Permission denied")

            # Delete the source using repository
            query = self.queries.DELETE_SOURCE
            params = {
                "source_id": request.source_id
            }

            execute_write_query(query, params)

            return organisation_pb2.DeleteSourceResponse(
                success=True,
                message="Source deleted successfully"
            )

        except Exception as e:
            logger.error("Error deleting source", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error deleting source: {str(e)}")
            return organisation_pb2.DeleteSourceResponse(success=False, message=f"Error deleting source: {str(e)}")